import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Layout from "../../components/Layout";
import ApplicationCard from "../../components/ApplicationCard";
import { Briefcase, Clock, CheckCircle, XCircle } from "lucide-react";

export default function Applications({ user, loading }) {
  const router = useRouter();
  const [applications, setApplications] = useState([]);
  const [filter, setFilter] = useState("all");
  const [loadingData, setLoadingData] = useState(true);

  useEffect(() => {
    if (!loading && !user) {
      router.push("/login");
    } else if (user) {
      fetchApplications();
    }
  }, [user, loading]);

  const fetchApplications = async () => {
    try {
      const res = await fetch("/api/applications/my-applications");
      const data = await res.json();
      setApplications(data.applications || []);
    } catch (error) {
      console.error("Failed to fetch applications:", error);
    } finally {
      setLoadingData(false);
    }
  };

  const filteredApplications = applications.filter((app) => {
    if (filter === "all") return true;
    return app.status === filter;
  });

  const stats = {
    total: applications.length,
    pending: applications.filter((a) => a.status === "pending").length,
    accepted: applications.filter((a) => a.status === "accepted").length,
    rejected: applications.filter((a) => a.status === "rejected").length,
    completed: applications.filter((a) => a.status === "completed").length,
  };

  if (loading || loadingData) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div>Loading...</div>
        </div>
      </Layout>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <Layout user={user}>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4">
          <h1 className="text-3xl font-bold mb-8">
            {user.user_type === "seeker"
              ? "Applications Received"
              : "My Applications"}
          </h1>

          {/* Stats Cards */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
            <button
              onClick={() => setFilter("all")}
              className={`p-4 rounded-lg transition-all ${
                filter === "all"
                  ? "bg-black text-white"
                  : "bg-white hover:shadow-md"
              }`}
            >
              <Briefcase className="mx-auto mb-2" size={24} />
              <div className="text-2xl font-bold">{stats.total}</div>
              <div className="text-sm">Total</div>
            </button>

            <button
              onClick={() => setFilter("pending")}
              className={`p-4 rounded-lg transition-all ${
                filter === "pending"
                  ? "bg-yellow-500 text-white"
                  : "bg-white hover:shadow-md"
              }`}
            >
              <Clock className="mx-auto mb-2" size={24} />
              <div className="text-2xl font-bold">{stats.pending}</div>
              <div className="text-sm">Pending</div>
            </button>

            <button
              onClick={() => setFilter("accepted")}
              className={`p-4 rounded-lg transition-all ${
                filter === "accepted"
                  ? "bg-green-500 text-white"
                  : "bg-white hover:shadow-md"
              }`}
            >
              <CheckCircle className="mx-auto mb-2" size={24} />
              <div className="text-2xl font-bold">{stats.accepted}</div>
              <div className="text-sm">Accepted</div>
            </button>

            <button
              onClick={() => setFilter("rejected")}
              className={`p-4 rounded-lg transition-all ${
                filter === "rejected"
                  ? "bg-red-500 text-white"
                  : "bg-white hover:shadow-md"
              }`}
            >
              <XCircle className="mx-auto mb-2" size={24} />
              <div className="text-2xl font-bold">{stats.rejected}</div>
              <div className="text-sm">Rejected</div>
            </button>

            <button
              onClick={() => setFilter("completed")}
              className={`p-4 rounded-lg transition-all ${
                filter === "completed"
                  ? "bg-purple-500 text-white"
                  : "bg-white hover:shadow-md"
              }`}
            >
              <CheckCircle className="mx-auto mb-2" size={24} />
              <div className="text-2xl font-bold">{stats.completed}</div>
              <div className="text-sm">Completed</div>
            </button>
          </div>

          {/* Applications List */}
          {filteredApplications.length === 0 ? (
            <div className="bg-white rounded-lg shadow p-12 text-center">
              <p className="text-gray-500">
                No {filter !== "all" ? filter : ""} applications found.
              </p>
            </div>
          ) : (
            <div className="grid gap-4">
              {filteredApplications.map((application) => (
                <ApplicationCard
                  key={application.id}
                  application={application}
                  user={user}
                  onUpdate={fetchApplications}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
